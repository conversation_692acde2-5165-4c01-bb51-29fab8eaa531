# Widget Templates Editor Plan

## Core Requirements
- Add widget template editing mode to existing editor
- <PERSON>ck single widget template in global state
- Modify left sidebar to show template name instead of pages
- Display widget tree with template as root
- Center editor shows template with magenta background
- No ability to add/delete templates yet

## Implementation Tasks

### 1. State Management
- Add widget template state to editor store
- Create mock widget template data structure
- Add editor mode state (normal/template editing)

### 2. Sidebar Modifications
- Detect template editing mode
- Replace pages list with template name display
- Keep widget tree functionality intact
- Set template as widget tree root

### 3. Center Editor Updates
- Add template editing view component
- Implement magenta background for template mode
- Ren<PERSON> selected template in center
- Maintain existing widget selection/editing

### 4. Navigation/Mode Switching
- Add way to enter template editing mode
- Add way to exit back to normal editor
- Preserve current editor state when switching

### 5. Widget Tree Integration
- Modify widget tree to use template as root when in template mode
- Ensure widget selection/editing works in template context
- Maintain property panel functionality

## Technical Notes
- Reuse existing widget editing components
- Template is just a reusable widget structure
- Keep editor functionality identical between modes
- Template editing isolated from page editing
